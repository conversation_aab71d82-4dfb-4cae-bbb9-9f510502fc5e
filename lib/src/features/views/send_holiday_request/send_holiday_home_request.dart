import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:page/src/core/extensions/extensions.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:page/src/features/models/booked_model.dart';
import 'package:page/src/features/views/send_holiday_request/success_request_screen.dart';

import '../../../core/config/constants.dart';
import '../../../core/localization/app_localizations.dart';
import '../../../core/response/profile_response.dart';
import '../../../core/services/api.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../bloc/profile_bloc.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/currency_controller.dart';

String numOfRooms(rooms) =>
    rooms != null ? rooms?.replaceAll('[', '').replaceAll(']', '') : '0';

// ignore: must_be_immutable
class SendHolidayRequest extends StatefulWidget {
  final int id;
  final int? rmsCategoryId;
  final String? numOfRooms;
  final String? propertyName;
  final int agentId;
  final num? startPrice;

  const SendHolidayRequest(
    this.id, {
    super.key,
    required this.rmsCategoryId,
    required this.numOfRooms,
    required this.agentId,
    required this.propertyName,
    required this.startPrice,
  });

  @override
  _SendRequest createState() => _SendRequest();
}

class _SendRequest extends State<SendHolidayRequest> {
  CurrencyController currencyController = CurrencyController();
  AuthController authController = AuthController();
  final TextEditingController messageController = TextEditingController();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  bool isLoading = false;

  List<BookedModel> bookedDays = [];
  List<(String date, num amount)> priceByDateDetails = [];

  // void getBookedDays() async {
  //   setState(() {
  //     isLoading = true;
  //   });
  //
  //   bookedDays = await Api.getBookedDays(widget.id);
  //
  //   setState(() {
  //     isLoading = false;
  //   });
  // }

  @override
  void initState() {
    currencyController.getcuurentcurrency(context);
    authController.isloggedin();
    bloc2.getProfile();
    // getBookedDays();
    super.initState();
  }

  final format2 = DateFormat("yyyy-MM-dd");

  void _populateUserData(ProfileResponse? profileData) {
    if (profileData?.results != null && authController.isLogged) {
      nameController.text = profileData!.results['fullname'] ?? '';
      phoneController.text = profileData.results['phone'] ?? '';
      emailController.text = profileData.results['email'] ?? '';
    }
  }

  // TextEditingController startdateController = TextEditingController();
  // TextEditingController enddateController = TextEditingController();
  TextEditingController promocode = TextEditingController();
  TextEditingController peopleController = TextEditingController();
  TextEditingController childrenController = TextEditingController(text: '0');
  bool isload = false;
  bool isPriceLoading = false;
  bool isfilstartdate = false;
  bool isfilenddate = false;
  String? currentvalue3;
  int? code;
  String? msg;
  Map<String, dynamic>? pricerequest;
  num total = 0.0;
  int? promocodeid;
  int usepromocode = 0;

  // DateTime? calculatedDateTime;

  ValueNotifier<List<DateTime?>> selectedDates =
      ValueNotifier<List<DateTime?>>([]);

  // arrival time, departure time
  ValueNotifier<String> arrivalTime = ValueNotifier<String>('15:00');
  ValueNotifier<String> departureTime = ValueNotifier<String>('11:00');

  void getpricerequest(int id) async {
    if (selectedDates.value.isEmpty) {
      return;
    }

    final startDate = selectedDates.value.first!;

    final endDate = format2.format(selectedDates.value.last!);

    // mke sure start date is after end date
    if (startDate.isAfter(selectedDates.value.last!)) {
      return;
    }

    final isSameDate = startDate.isSameDay(selectedDates.value.last!);

    if (isSameDate) {
      return;
    }

    final date = format2.format(startDate);

    setState(() {
      isPriceLoading = true;
    });

    await Api.getholidayhomerequestprice(
            id,
            date,
            endDate,
            promocodeid,
            numOfRooms(widget.numOfRooms),
            peopleController.text,
            childrenController.text)
        .then((value) {
      value != null
          ? setState(() {
              code = value.code;
              msg = value.error;
              pricerequest = value.results;
              total = pricerequest?['total_amount'] ?? 0;
              //   "rms_response": {
              //             "rateBreakdown": [
              //                 {
              //                     "theDate": "2024-11-26 00:00:00",
              //                     "baseRateAmount": 0,}]}

              priceByDateDetails = List<Map>.from(pricerequest?['rms_response'])
                  .map(
                    (e) => (
                      (e['theDate'] ?? '') as String,
                      (e['baseRateAmount'] ?? 0) as num
                    ),
                  )
                  .toList();
              isPriceLoading = false;
            })
          : null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF27b4a8),
        centerTitle: true,
        title: Container(
          padding: const EdgeInsets.only(left: 20, right: 20),
          child: Text(
            AppLocalizations.of(context).translate(
              'Send Request',
            ),
            style: TextStyle(
              fontFamily: isEnglish(context) ? 'Roboto' : 'Tajawal',
            ),
          ),
        ),
      ),
      body: StreamBuilder<ProfileResponse>(
        stream: bloc2.subject.stream,
        builder: (context, AsyncSnapshot<ProfileResponse> snapshot) {
          if (isLoading) {
            return buildLoadingWidget();
          }
          // Populate user data when logged in
          _populateUserData(snapshot.data);

          return SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context).translate('Send Request'),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    const SizedBox(height: 24),

                    // User Name Field
                    TextFormField(
                      controller: nameController,
                      enabled: !authController.isLogged,
                      decoration: InputDecoration(
                        labelText:
                            AppLocalizations.of(context).translate('Name'),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      validator: (value) {
                        if (!authController.isLogged &&
                            (value == null || value.isEmpty)) {
                          return AppLocalizations.of(context)
                              .translate('Please enter your name');
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 12),

                    // Phone and Email Row
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: phoneController,
                            enabled: !authController.isLogged,
                            decoration: InputDecoration(
                              labelText: AppLocalizations.of(context)
                                  .translate('Phone'),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            validator: (value) {
                              if (!authController.isLogged &&
                                  (value == null || value.isEmpty)) {
                                return AppLocalizations.of(context)
                                    .translate('Please enter your phone');
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: TextFormField(
                            controller: emailController,
                            enabled: !authController.isLogged,
                            decoration: InputDecoration(
                              labelText: AppLocalizations.of(context)
                                  .translate('Email'),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            // validator: (value) {
                            //   if (!authController.isLogged &&
                            //       (value == null || value.isEmpty)) {
                            //     return AppLocalizations.of(context)
                            //         .translate('Please enter your email');
                            //   }
                            //   return null;
                            // },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    TextFormField(
                      enabled: false,
                      initialValue: widget.propertyName ?? '',
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context)
                            .translate('Property Name'),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),
                    TextFormField(
                      enabled: false,
                      initialValue:
                          '${widget.startPrice ?? ''} ${currencyController.currency}',
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context)
                            .translate('Property Price'),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Message Field
                    TextFormField(
                      controller: messageController,
                      enabled: true,
                      minLines: 3,
                      maxLines: 3,
                      decoration: InputDecoration(
                        labelText:
                            AppLocalizations.of(context).translate('Message'),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return AppLocalizations.of(context)
                              .translate('Please enter your message');
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 24),

                    // Send Button
                    !isload
                        ? SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: primaryColor,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                              ),
                              onPressed: () async {
                                // Validate form
                                if (!_formKey.currentState!.validate()) {
                                  return;
                                }

                                // Check login for non-logged users
                                if (!authController.isLogged) {
                                  // For non-logged users, all fields should be filled
                                  if (nameController.text.isEmpty ||
                                      phoneController.text.isEmpty) {
                                    snackbar(AppLocalizations.of(context)
                                        .translate(
                                            'Please fill all required fields'));
                                    return;
                                  }
                                }

                                setState(() {
                                  isload = !isload;
                                });

                                try {
                                  await Api.sendMessage(
                                    propertyId: widget.id,
                                    propertyName:
                                        widget.propertyName?.toString() ?? '',
                                    propertyPrice:
                                        widget.startPrice?.toString() ?? '',
                                    message: messageController.text,
                                    userName: authController.isLogged
                                        ? snapshot.data?.results['fullname'] ??
                                            ''
                                        : nameController.text,
                                    userPhone: authController.isLogged
                                        ? snapshot.data?.results['phone'] ?? ''
                                        : phoneController.text,
                                    userEmail: authController.isLogged
                                        ? snapshot.data?.results['email'] ?? ''
                                        : emailController.text,
                                    userId: authController.isLogged
                                        ? snapshot.data?.results['id']
                                            ?.toString()
                                        : null,
                                  );

                                  setState(() {
                                    isload = !isload;
                                  });

                                  snackbar2(AppLocalizations.of(context)
                                      .translate('Sent Successfully'));

                                  Navigator.of(context).pushReplacement(
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            const SuccessRequestScreen(
                                              isFromSendRequest: true,
                                            )),
                                  );
                                } catch (e) {
                                  log('Cannot send message: $e');
                                  snackbar(AppLocalizations.of(context).translate(
                                      'Something went wrong, please try again later'));
                                  setState(() {
                                    isload = !isload;
                                  });
                                }
                              },
                              child: Text(
                                AppLocalizations.of(context).translate('Send'),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          )
                        : Center(
                            child: Lottie.asset(
                              'assets/59218-progress-indicator.json',
                              height: 50,
                              width: 50,
                            ),
                          ),
                    const SizedBox(height: 16),
                  ],
                ),
              ));
        },
      ),
    ));
  }
}
