import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:page/src/features/views/account/account.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/config/constants.dart';
import '../../../../core/localization/app_localizations.dart';
import '../../../../core/response/profile_response.dart';
import '../../../../core/services/api.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../bloc/profile_bloc.dart';
import '../../../controllers/auth_controller.dart';
import '../../../controllers/currency_controller.dart';
import '../../property_details/widgets/property_details.dart';

class CallActionsWidget extends StatefulWidget {
  final onCancel;
  final int id;
  final String propertyName;
  final String propertyPrice;
  final CurrencyController currencyController;

  const CallActionsWidget({
    super.key,
    required this.id,
    required this.onCancel,
    required this.propertyName,
    required this.propertyPrice,
    required this.currencyController,
  });

  @override
  State<CallActionsWidget> createState() => _CallActionsWidgetState();
}

class _CallActionsWidgetState extends State<CallActionsWidget> {
  AuthController authController = AuthController();
  final TextEditingController messageController = TextEditingController();

  @override
  void initState() {
    super.initState();
    authController.isloggedin();
    bloc2.getProfile();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      color: Colors.grey.withOpacity(0.4),
      child: Container(
          padding: const EdgeInsets.only(
            left: 10,
            right: 10,
            bottom: 20,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  color: Colors.grey[100],
                ),
                width: MediaQuery.of(context).size.width,
                child: Column(
                  children: [
                    const SizedBox(
                      height: 20,
                    ),
                    InkWell(
                      onTap: () async {
                        try {
                          final phone = appConfiguration?.results['phone'] ??
                              '+97142454824';
                          final String phoneNumber =
                              phone.replaceAll(RegExp(r'[^0-9]'), '');

                          log('Call Phone $phoneNumber');

                          await callNumber(phoneNumber);
                          // await launchUrlString("tel:$phoneNumber");
                        } catch (e) {
                          log('Cannot open $e');
                        }
                      },
                      child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: Center(
                              child: Text(
                            AppLocalizations.of(context).translate('Call'),
                            style: const TextStyle(
                                color: Color(0xff007AFF), fontSize: 18),
                          ))),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Divider(
                      color: Colors.green[100],
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    StreamBuilder<ProfileResponse>(
                      stream: bloc2.subject.stream,
                      builder:
                          (context, AsyncSnapshot<ProfileResponse> snapshot) {
                        return Column(
                          children: [
                            InkWell(
                                onTap: () async {
                                  try {
                                    final String whatsapp =
                                        '${appConfiguration?.results['whatsapp'] ?? '+97142454824'}';

                                    final body =
                                        'Property Name: ${widget.propertyName}\n'
                                        'Property Price: ${widget.propertyPrice} ${widget.currencyController.currency}';

                                    var whatsappUrl = Uri.parse(
                                        "https://wa.me/$whatsapp?text=$body");

                                    await launchUrl(whatsappUrl,
                                        mode: LaunchMode.externalApplication);
                                  } catch (e) {
                                    log('Cannot open $e');
                                  }
                                },
                                child: SizedBox(
                                    width: MediaQuery.of(context).size.width,
                                    child: Center(
                                      child: Text(
                                        AppLocalizations.of(context)
                                            .translate('Whatsapp'),
                                        style: const TextStyle(
                                            color: Color(0xff007AFF),
                                            fontSize: 18),
                                      ),
                                    ))),
                            const SizedBox(
                              height: 10,
                            ),
                            Divider(
                              color: Colors.green[100],
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            InkWell(
                                onTap: () async {
                                  // if (authController.isLogged == false) {
                                  //   snackbar(AppLocalizations.of(context)
                                  //       .translate('Please login first'));
                                  //   return;
                                  // }

                                  final userName =
                                      snapshot.data?.results['fullname'] ?? '';
                                  final userEmail =
                                      snapshot.data?.results['email'] ?? '';
                                  final userPhone =
                                      snapshot.data?.results['phone'] ?? '';

                                  showModalBottomSheet(
                                    context: context,
                                    isScrollControlled: true,
                                    shape: const RoundedRectangleBorder(
                                      borderRadius: BorderRadius.vertical(
                                          top: Radius.circular(20)),
                                    ),
                                    builder: (context) {
                                      final isEng =
                                          Localizations.localeOf(context)
                                                  .languageCode ==
                                              'en';
                                      return Padding(
                                        padding:
                                            MediaQuery.of(context).viewInsets,
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Stack(
                                              children: [
                                                Align(
                                                  alignment: Alignment.center,
                                                  child: Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        vertical: 16),
                                                    child: Text(
                                                      AppLocalizations.of(
                                                              context)
                                                          .translate(
                                                              'Request Call Back'),
                                                      style: const TextStyle(
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          fontSize: 18),
                                                    ),
                                                  ),
                                                ),
                                                Align(
                                                  alignment: isEng
                                                      ? Alignment.topRight
                                                      : Alignment.topLeft,
                                                  child: IconButton(
                                                    icon:
                                                        const Icon(Icons.close),
                                                    onPressed: () =>
                                                        Navigator.of(context)
                                                            .pop(),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 24,
                                                      vertical: 8),
                                              child: Column(
                                                children: [
                                                  TextFormField(
                                                    enabled: !authController
                                                        .isLogged,
                                                    initialValue: userName,
                                                    decoration: InputDecoration(
                                                      labelText:
                                                          AppLocalizations.of(
                                                                  context)
                                                              .translate(
                                                                  'Name'),
                                                      border:
                                                          OutlineInputBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(height: 12),
                                                  Row(
                                                    children: [
                                                      Expanded(
                                                        child: TextFormField(
                                                          enabled:
                                                              !authController
                                                                  .isLogged,
                                                          initialValue:
                                                              userPhone,
                                                          decoration:
                                                              InputDecoration(
                                                            labelText:
                                                                AppLocalizations.of(
                                                                        context)
                                                                    .translate(
                                                                        'Phone'),
                                                            border:
                                                                OutlineInputBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          12),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      const SizedBox(width: 12),
                                                      Expanded(
                                                        child: TextFormField(
                                                          enabled:
                                                              !authController
                                                                  .isLogged,
                                                          initialValue:
                                                              userEmail,
                                                          decoration:
                                                              InputDecoration(
                                                            labelText:
                                                                AppLocalizations.of(
                                                                        context)
                                                                    .translate(
                                                                        'Email'),
                                                            border:
                                                                OutlineInputBorder(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          12),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  const SizedBox(height: 12),
                                                  TextFormField(
                                                    enabled: false,
                                                    initialValue:
                                                        widget.propertyName,
                                                    decoration: InputDecoration(
                                                      labelText: AppLocalizations
                                                              .of(context)
                                                          .translate(
                                                              'Property Name'),
                                                      border:
                                                          OutlineInputBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(height: 12),
                                                  TextFormField(
                                                    enabled: false,
                                                    initialValue:
                                                        '${widget.propertyPrice} ${widget.currencyController.currency}',
                                                    decoration: InputDecoration(
                                                      labelText: AppLocalizations
                                                              .of(context)
                                                          .translate(
                                                              'Property Price'),
                                                      border:
                                                          OutlineInputBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(height: 12),
                                                  TextFormField(
                                                    controller:
                                                        messageController,
                                                    enabled: true,
                                                    minLines: 3,
                                                    maxLines: 3,
                                                    decoration: InputDecoration(
                                                      labelText:
                                                          AppLocalizations.of(
                                                                  context)
                                                              .translate(
                                                                  'Message'),
                                                      border:
                                                          OutlineInputBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(height: 36),
                                                  SizedBox(
                                                    width: double.infinity,
                                                    child: ElevatedButton(
                                                      style: ElevatedButton
                                                          .styleFrom(
                                                        backgroundColor:
                                                            primaryColor,
                                                        shape:
                                                            RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(12),
                                                        ),
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                vertical: 16),
                                                      ),
                                                      onPressed: () async {
                                                        Navigator.of(context)
                                                            .pop();
                                                        try {
                                                          Api.sendMessage(
                                                            propertyId:
                                                                widget.id,
                                                            propertyName: widget
                                                                .propertyName,
                                                            propertyPrice: widget
                                                                .propertyPrice,
                                                            message:
                                                                messageController
                                                                    .text,
                                                          );
                                                          snackbar2(AppLocalizations
                                                                  .of(context)
                                                              .translate(
                                                                  'Sent Successfully'));
                                                        } catch (e) {
                                                          log('Cannot open $e');
                                                          snackbar(AppLocalizations
                                                                  .of(context)
                                                              .translate(
                                                                  'Something went wrong, please try again later'));
                                                        }
                                                      },
                                                      child: Text(
                                                        AppLocalizations.of(
                                                                context)
                                                            .translate('Send'),
                                                        style: const TextStyle(
                                                            color: Colors.white,
                                                            fontSize: 16),
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(height: 100),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  );
                                },
                                child: SizedBox(
                                    width: MediaQuery.of(context).size.width,
                                    child: Center(
                                      child: Text(
                                        AppLocalizations.of(context)
                                            .translate('Request Call Back'),
                                        style: const TextStyle(
                                            color: Color(0xff007AFF),
                                            fontSize: 18),
                                      ),
                                    )))
                          ],
                        );
                      },
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              InkWell(
                  onTap: widget.onCancel,
                  child: Container(
                      child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: Colors.white,
                    ),
                    width: MediaQuery.of(context).size.width,
                    child: Center(
                      child: Text(
                        AppLocalizations.of(context).translate('Cancel'),
                        style: const TextStyle(
                            color: Color(0xff007AFF), fontSize: 18),
                      ),
                    ),
                  )))
            ],
          )),
    );
  }
}
